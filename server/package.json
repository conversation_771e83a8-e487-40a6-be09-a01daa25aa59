{"name": "boatbook-server", "version": "1.0.0", "description": "BoatBook API Server", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "echo 'No build step needed for server'", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"axios": "^1.10.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.21.2", "helmet": "^8.1.0", "node-cache": "^5.1.2"}, "devDependencies": {"jest": "^30.0.4", "nodemon": "^3.1.10", "supertest": "^7.1.3"}, "keywords": ["weather", "api", "noaa", "boating"], "author": "BoatBook", "license": "MIT"}