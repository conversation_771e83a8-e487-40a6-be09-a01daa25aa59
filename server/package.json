{"name": "boatbook-server", "version": "1.0.0", "description": "BoatBook API Server", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "echo 'No build step needed for server'", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"axios": "^1.6.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "node-cache": "^5.1.2"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.4"}, "keywords": ["weather", "api", "noaa", "boating"], "author": "BoatBook", "license": "MIT"}